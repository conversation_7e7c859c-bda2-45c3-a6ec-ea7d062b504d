{"name": "em<PERSON><PERSON><PERSON>-graham", "version": "1.0.0", "license": "MIT", "main": "vite.config.js", "keywords": [], "type": "module", "scripts": {"logout": "shopify auth logout", "dev": "run-p -sr \"shopify:dev -- {@}\" \"vite:dev\" --", "deploy": "run-s \"vite:build\" \"shopify:push -- {@}\" --", "shopify:dev": "shopify theme dev", "shopify:push": "shopify theme push", "vite:dev": "vite", "vite:build": "vite build"}, "devDependencies": {"autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "npm-run-all": "^4.1.5", "postcss": "^8.5.3", "sass": "^1.81.0", "tailwindcss": "^4.1.7", "vite": "^5.4.19", "vite-plugin-shopify": "^3.2.0"}, "prettier": {"singleQuote": true}, "dependencies": {"@by-association-only/vite-plugin-shopify-clean": "^2.0.0", "@tailwindcss/vite": "^4.1.7", "swiper": "^11.2.6"}}