.page-width {
	@apply ts:md:!px-12 ts:!px-8;
}
.page-width-full {
	@apply ts:!px-0
} 
.text-align {
  text-align: var(--text-align);
}
.custom-section {
	@apply ts:xl:py-[var(--padding-xl)] ts:lg:py-[var(--padding-lg)] ts:md:py-[var(--padding-md)] ts:py-[var(--padding-sm)] ts:px-0 ts:overflow-hidden;
}
.button {
	@apply ts:text-xs ts:leading-none ts:md:min-w-[17.5rem] ts:min-w-[14rem] ts:hover:!text-[rgba(var(--color-foreground))] ts:hover:!bg-[rgba(var(--color-background))] ts:hover:after:!shadow-none ts:border ts:border-[rgba(var(--color-background))];
}
.btn-text-only {
	@apply ts:!text-sm ts:uppercase ts:font-normal ts:inline ts:p-0 ts:bg-transparent ts:text-[rgba(var(--color-foreground))] ts:hover:font-bold ts:hover:underline ts:transition-all ts:duration-300 ts:ease-linear;
}
.button--secondary {
	@apply ts:after:!shadow-none ts:hover:!bg-[rgba(var(--color-foreground))] ts:hover:!text-[rgba(var(--color-background))];
}
button, a, .button, .btn {
	@apply ts:transition-all ts:duration-300 ts:ease-linear;
}
.rte {
	@apply ts:space-y-8;
}
header .header__menu-item,
header .list-menu__item,
header .menu-drawer__account {
	@apply ts:text-sm;
}
.footer,
.footer__content-bottom {
	@apply ts:!border-0;
}
.footer .footer__blocks-wrapper .footer-block {
	@apply ts:flex ts:2xl:gap-x-20 ts:gap-x-10;
}
.footer .footer-block__heading {
	@apply ts:font-body ts:text-xs ts:font-bold ts:uppercase ts:basis-36 ts:mb-0;
}
.footer .footer-block__details-content li {
	@apply ts:leading-none;
}
.footer .footer-block__details-content > :first-child .list-menu__item--link {
	@apply ts:!leading-none;
}
.footer .footer-block__details-content .list-menu__item {
	@apply ts:!leading-[1.25] ts:text-xs ts:uppercase ts:text-[rgba(var(--color-foreground))];
}
.footer .footer__content-bottom-wrapper {
	@apply ts:[@media(width<=749px)]:flex-col-reverse ts:[@media(width<=749px)]:items-center;
}
.footer .localization-form {
	@apply ts:m-0 ts:p-0;
}
.footer .localization-selector {
	@apply ts:before:!shadow-none ts:after:!shadow-none ts:!m-0 ts:p-0 ts:h-auto ts:min-h-0 ts:underline;
}
.footer .localization-selector > span {
	@apply ts:flex ts:items-center ts:gap-x-3 ts:text-xs;
}
.footer .localization-selector .icon-triangle {
	@apply ts:w-5 ts:ml-4 ts:transition-all ts:duration-300;
}
.footer .localization-selector[aria-expanded="false"] .icon-triangle {
	@apply ts:rotate-180;
}
accordion-item .icon-caret {
	@apply ts:w-5 ts:h-5 ts:transition-all ts:duration-300;
}
accordion-item[open] .icon-caret {
	@apply ts:rotate-180;
}