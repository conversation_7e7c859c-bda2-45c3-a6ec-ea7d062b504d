customElements.define('blog-filter', class extends HTMLElement {
  connectedCallback() {
    this.form = this.querySelector('form');
    this.filterUrl = this.dataset.url;
    this.sectionId = this.dataset.sectionId;
    this.blogPostsContainer = document.querySelector('#blog-posts');

    this.form.addEventListener('change', () => this.onFilter());
    this.form.addEventListener('reset', (e) => {
      e.preventDefault();
      this.form.reset();
      requestAnimationFrame(() => this.onFilter());
    });
  }

  async onFilter() {
    const formData = new FormData(this.form);
    const params = new URLSearchParams();

    const year = formData.get('year');
    const categories = formData.getAll('category');

    if (year) params.set('year', year);
    if (categories.length) params.set('category', categories.join(','));

    params.set('section_id', this.sectionId);

    this.setLoading(true);
    const res = await fetch(`${this.filterUrl}?${params.toString()}`);
    const html = await res.text();
    const parser = new DOMParser().parseFromString(html, 'text/html');
    const updated = parser.querySelector('#blog-posts');
    console.log((`${this.filterUrl}?${params.toString()}`))

    if (updated) {
      this.blogPostsContainer.innerHTML = updated.innerHTML;
    }
    this.setLoading(false);
  }

  setLoading(state) {
    this.blogPostsContainer.classList.toggle('ts:opacity-50', state);
    this.blogPostsContainer.classList.toggle('ts:pointer-events-none', state);
  }
});