{"name": "t:sections.footer.name", "type": "footer", "sections": {"rich_text_4YrANV": {"type": "rich-text", "blocks": {"text_gXWg6B": {"type": "text", "settings": {"text": "<p>EMMANUELA GRAHAM</p>"}}}, "block_order": ["text_gXWg6B"], "custom_css": [".rich-text__wrapper {width: 100%; padding: 0 !important;}", ".rich-text__blocks {max-width: 100%;}", ".rich-text__text {font-size: var(--ts-text-big); line-height: 1.2;}"], "name": "t:sections.rich-text.presets.name", "settings": {"desktop_content_position": "center", "content_alignment": "center", "color_scheme": "", "container_width": 1920, "full_width": true, "padding_top": 4, "padding_bottom": 4}}, "footer": {"type": "footer", "blocks": {"link_list_hQrhWC": {"type": "link_list", "settings": {"heading": "Connect", "menu": "footer"}}, "link_list_GeMryN": {"type": "link_list", "settings": {"heading": "Info", "menu": "footer-info"}}, "link_list_4T7CJH": {"type": "link_list", "settings": {"heading": "The Company", "menu": "footer-company"}}, "link_list_ejtnHx": {"type": "link_list", "settings": {"heading": "Social", "menu": "footer-social"}}}, "block_order": ["link_list_hQrhWC", "link_list_GeMryN", "link_list_4T7CJH", "link_list_ejtnHx"], "custom_css": ["@media screen and (min-width: 1441px) {.grid {--grid-desktop-horizontal-spacing: 8rem; }}"], "settings": {"color_scheme": "scheme-1", "newsletter_enable": false, "newsletter_heading": "Subscribe to our emails", "enable_follow_on_shop": false, "show_social": false, "enable_country_selector": true, "enable_language_selector": false, "payment_enable": false, "show_policy": false, "margin_top": 0, "padding_top": 64, "padding_bottom": 36}}}, "order": ["rich_text_4YrANV", "footer"]}