swiper-slider .bordered-card .swiper-slide .card-wrapper .card__inner {
	border: 1px solid rgba(var(--color-secondary-button-text));
}
swiper-slider .swiper-slide .card-wrapper .card__information,
swiper-slider .swiper-slide .card-wrapper .card-information {
	text-align: var(--text-align);
}
swiper-slider {
	@apply ts:relative;
}
swiper-slider,
.swiper-controls {
	--swiper-theme-color: rgba(var(--color-foreground));
}
.swiper-controls .swiper-nav {
	@apply ts:lg:w-10 ts:lg:h-10 ts:w-6 ts:h-6 ts:z-[2] ts:absolute ts:top-1/2 ts:-translate-y-1/2;
}
.swiper-controls .swiper-nav.swiper-button-next {
	@apply ts:rotate-180
}
.swiper-controls .swiper-button-next.swiper-button-disabled,
.swiper-controls .swiper-button-prev.swiper-button-disabled {
	@apply ts:opacity-20
}
.swiper-controls .swiper-button-prev {
	@apply ts:left-0
}
.swiper-controls .swiper-button-next {
	@apply ts:right-8
}
.swiper-controls .swiper-button-next,
.swiper-controls .swiper-button-prev {
	@apply ts:after:content-[""]
}
.wrapper.inline-title swiper-slider {
	@apply ts:lg:col-span-9 ts:md:col-span-8 ts:md:-mr-12;
}
.wrapper.inline-title swiper-slider .card--product {
	@apply ts:xl:flex-row ts:xl:gap-4;
}
.wrapper.inline-title swiper-slider .card__inner ~ .card__content > .card__information {
	@apply ts:xl:!p-0 ts:xl:flex ts:xl:flex-col ts:xl:justify-between;
}
.wrapper.inline-title swiper-slider .card__heading {
	@apply ts:!text-base ts:!font-normal;
}
.wrapper.inline-title swiper-slider .card-information .price {
	@apply ts:!text-base ts:font-bold;
}
swiper-slider.grid-view .swiper-controls {
	@apply ts:hidden;
}
swiper-slider.grid-view .swiper-wrapper {
	@apply ts:!grid ts:grid-cols-2 ts:gap-x-4 ts:gap-y-10;
}
swiper-slider .swiper-center-mode .swiper-slide {
	@apply ts:transition-all ts:duration-300;
}
swiper-slider:not(.grid-view) .swiper-center-mode .swiper-slide:not(.swiper-slide-active) {
	@apply ts:opacity-50;
}
swiper-slider .swiper-scrollbar.swiper-scrollbar-horizontal {
	@apply ts:w-full ts:h-[2px] ts:rounded-none ts:left-0 ts:-bottom-10 ts:bg-[#F5F5F5];
}
swiper-slider .swiper-scrollbar-drag {
	@apply ts:block ts:bg-gray ts:h-[4px] ts:top-[-1px] ts:rounded-none;
}