<script>
	window.shopUrl = '{{ request.origin }}';
	window.routes = {
		cart_add_url: '{{ routes.cart_add_url }}',
		cart_change_url: '{{ routes.cart_change_url }}',
		cart_update_url: '{{ routes.cart_update_url }}',
		cart_url: '{{ routes.cart_url }}',
		predictive_search_url: '{{ routes.predictive_search_url }}',
	};

	window.cartStrings = {
		error: `{{ 'sections.cart.cart_error' | t }}`,
		quantityError: `{{ 'sections.cart.cart_quantity_error_html' | t: quantity: '[quantity]' }}`,
	};

	window.variantStrings = {
		addToCart: `{{ 'products.product.add_to_cart' | t }}`,
		soldOut: `{{ 'products.product.sold_out' | t }}`,
		unavailable: `{{ 'products.product.unavailable' | t }}`,
		unavailable_with_option: `{{ 'products.product.value_unavailable' | t: option_value: '[value]' }}`,
	};

	window.quickOrderListStrings = {
		itemsAdded: `{{ 'sections.quick_order_list.items_added.other' | t: quantity: '[quantity]' }}`,
		itemAdded: `{{ 'sections.quick_order_list.items_added.one' | t: quantity: '[quantity]' }}`,
		itemsRemoved: `{{ 'sections.quick_order_list.items_removed.other' | t: quantity: '[quantity]' }}`,
		itemRemoved: `{{ 'sections.quick_order_list.items_removed.one' | t: quantity: '[quantity]' }}`,
		viewCart: `{{- 'sections.quick_order_list.view_cart' | t -}}`,
		each: `{{- 'sections.quick_order_list.each' | t: money: '[money]' }}`,
		min_error: `{{- 'sections.quick_order_list.min_error' | t: min: '[min]' }}`,
		max_error: `{{- 'sections.quick_order_list.max_error' | t: max: '[max]' }}`,
		step_error: `{{- 'sections.quick_order_list.step_error' | t: step: '[step]' }}`,
	};

	window.accessibilityStrings = {
		imageAvailable: `{{ 'products.product.media.image_available' | t: index: '[index]' }}`,
		shareSuccess: `{{ 'general.share.success_message' | t }}`,
		pauseSlideshow: `{{ 'sections.slideshow.pause_slideshow' | t }}`,
		playSlideshow: `{{ 'sections.slideshow.play_slideshow' | t }}`,
		recipientFormExpanded: `{{ 'recipient.form.expanded' | t }}`,
		recipientFormCollapsed: `{{ 'recipient.form.collapsed' | t }}`,
		countrySelectorSearchCount: `{{ 'localization.country_results_count' | t: count: '[count]' }}`,
	};
</script>

{%- if settings.predictive_search_enabled -%}
	<script src="{{ 'predictive-search.js' | asset_url }}" defer="defer"></script>
{%- endif -%}

{%- if settings.cart_type == 'drawer' -%}
	<script src="{{ 'cart-drawer.js' | asset_url }}" defer="defer"></script>
{%- endif -%}